'use client';

import React, { useRef, useEffect, useMemo } from 'react';
import { random } from '@/lib/random';

interface IndustrialPulseShapesProps {
  progress: number;
}

// --- Particle Class ---
class Particle {
  x: number;
  y: number;
  size: number;
  initialSize: number;
  vx: number;
  vy: number;
  opacity: number;
  initialOpacity: number;
  life: number;
  maxLife: number;

  constructor(canvasWidth: number, canvasHeight: number, plumeX: number) {
    this.x = plumeX + random(-canvasWidth * 0.05, canvasWidth * 0.05);
    this.y = canvasHeight + random(0, 50);
    this.initialSize = random(canvasWidth * 0.1, canvasWidth * 0.25); // Further increased size
    this.size = this.initialSize;
    this.vx = random(-0.3, 0.3);
    this.vy = random(-0.5, -1.5);
    this.maxLife = random(200, 400);
    this.life = this.maxLife;
    this.initialOpacity = random(0.3, 0.7); // Further increased opacity
    this.opacity = 0;
  }

  update() {
    this.x += this.vx;
    this.y += this.vy;
    this.life--;

    const lifeRatio = this.life / this.maxLife;
    if (lifeRatio > 0.8) {
      this.opacity = this.initialOpacity * ((1 - lifeRatio) / 0.2);
    } else {
      this.opacity = this.initialOpacity * lifeRatio;
    }
    this.size = this.initialSize * (1 + (1 - lifeRatio) * 0.8);
  }

  draw(ctx: CanvasRenderingContext2D, preRenderedParticle: HTMLCanvasElement) {
    if (this.opacity <= 0) return;
    ctx.save();
    ctx.globalAlpha = this.opacity;
    const drawSize = this.size * 2;
    ctx.drawImage(preRenderedParticle, this.x - this.size, this.y - this.size, drawSize, drawSize);
    ctx.restore();
  }
}

// --- Component ---
const IndustrialPulseShapes: React.FC<IndustrialPulseShapesProps> = ({ progress }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const progressRef = useRef(progress);

  // Keep a ref to the latest progress value
  useEffect(() => {
    progressRef.current = progress;
  }, [progress]);

  const smokePlumes = useMemo(() => [0.2, 0.5, 0.8], []);

  // Main animation effect, runs only once on mount
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const maxParticles = 350; // Increased particle count for density

    // --- Performance Optimization: Pre-render a single particle --- //
    const preRenderedParticle = document.createElement('canvas');
    const preRenderedCtx = preRenderedParticle.getContext('2d');
    const particleRenderSize = 128;
    preRenderedParticle.width = particleRenderSize;
    preRenderedParticle.height = particleRenderSize;
    if (preRenderedCtx) {
      const gradient = preRenderedCtx.createRadialGradient(
        particleRenderSize / 2,
        particleRenderSize / 2,
        0,
        particleRenderSize / 2,
        particleRenderSize / 2,
        particleRenderSize / 2
      );
      gradient.addColorStop(0, 'rgba(200, 200, 200, 0.5)');
      gradient.addColorStop(1, 'rgba(200, 200, 200, 0)');
      preRenderedCtx.fillStyle = gradient;
      preRenderedCtx.fillRect(0, 0, particleRenderSize, particleRenderSize);
    }
    // --- End of Pre-rendering --- //

    const resizeCanvas = () => {
      const dpr = window.devicePixelRatio || 1;
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      ctx.scale(dpr, dpr);
    };

    const animate = () => {
      const currentProgress = progressRef.current;
      const targetParticleCount = Math.floor(Math.pow(currentProgress, 1.5) * maxParticles);

      if (particlesRef.current.length < targetParticleCount) {
        const plumeIndex = particlesRef.current.length % smokePlumes.length;
        const logicalWidth = canvas.width / (window.devicePixelRatio || 1);
        const logicalHeight = canvas.height / (window.devicePixelRatio || 1);
        const plumeX = logicalWidth * smokePlumes[plumeIndex];
        particlesRef.current.push(new Particle(logicalWidth, logicalHeight, plumeX));
      }

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particlesRef.current = particlesRef.current.filter(p => p.life > 0);
      // Add new particles based on progress
      if (particlesRef.current.length < maxParticles && Math.random() < progress * 0.9) { // Increased emission rate
        const plumeIndex = Math.floor(Math.random() * smokePlumes.length);
        const plumeX = canvas.width * smokePlumes[plumeIndex];
        particlesRef.current.push(new Particle(canvas.width, canvas.height, plumeX));
      }

      // Update and draw particles
      for (const p of particlesRef.current) {
        p.update();
        p.draw(ctx, preRenderedParticle);
      }

      animationFrameId.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    animate();

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      particlesRef.current = [];
    };
  }, [smokePlumes]); // Dependency array is now stable

  return (
    <div className="absolute inset-0 w-full h-full" style={{ mixBlendMode: 'screen' }}>
      <canvas ref={canvasRef} className="w-full h-full" />
    </div>
  );
};

export default IndustrialPulseShapes;
