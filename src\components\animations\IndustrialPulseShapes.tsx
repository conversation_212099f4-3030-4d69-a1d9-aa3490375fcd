'use client';

import React, { useRef, useEffect, useMemo } from 'react';
import { random } from '@/lib/random';

interface IndustrialPulseShapesProps {
  progress: number;
}

// --- Particle Class ---
class Particle {
  x: number;
  y: number;
  size: number;
  initialSize: number;
  vx: number;
  vy: number;
  opacity: number;
  initialOpacity: number;
  life: number;
  maxLife: number;

  constructor(canvasWidth: number, canvasHeight: number, plumeX: number) {
    this.x = plumeX + random(-canvasWidth * 0.05, canvasWidth * 0.05);
    this.y = canvasHeight + random(0, 50);
    this.initialSize = random(canvasWidth * 0.05, canvasWidth * 0.15);
    this.size = this.initialSize;
    this.vx = random(-0.3, 0.3);
    this.vy = random(-0.5, -1.5);
    this.maxLife = random(200, 400);
    this.life = this.maxLife;
    this.initialOpacity = random(0.05, 0.2);
    this.opacity = 0;
  }

  update() {
    this.x += this.vx;
    this.y += this.vy;
    this.life--;

    const lifeRatio = this.life / this.maxLife;
    if (lifeRatio > 0.8) {
      this.opacity = this.initialOpacity * ((1 - lifeRatio) / 0.2);
    } else {
      this.opacity = this.initialOpacity * lifeRatio;
    }
    this.size = this.initialSize * (1 + (1 - lifeRatio) * 0.8);
  }

  draw(ctx: CanvasRenderingContext2D) {
    if (this.opacity <= 0) return;
    ctx.save();
    const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.size);
    gradient.addColorStop(0, `rgba(160, 160, 160, ${this.opacity * 0.5})`);
    gradient.addColorStop(1, `rgba(160, 160, 160, 0)`);
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.fill();
    ctx.restore();
  }
}

// --- Component ---
const IndustrialPulseShapes: React.FC<IndustrialPulseShapesProps> = ({ progress }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationFrameId = useRef<number>();
  const progressRef = useRef(progress);

  // Keep a ref to the latest progress value
  useEffect(() => {
    progressRef.current = progress;
  }, [progress]);

  const smokePlumes = useMemo(() => [0.2, 0.5, 0.8], []);

  // Main animation effect, runs only once on mount
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const maxParticles = 150;

    const resizeCanvas = () => {
      const dpr = window.devicePixelRatio || 1;
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      ctx.scale(dpr, dpr);
    };

    const animate = () => {
      const currentProgress = progressRef.current;
      const targetParticleCount = Math.floor(Math.pow(currentProgress, 1.5) * maxParticles);

      if (particlesRef.current.length < targetParticleCount) {
        const plumeIndex = particlesRef.current.length % smokePlumes.length;
        const logicalWidth = canvas.width / (window.devicePixelRatio || 1);
        const logicalHeight = canvas.height / (window.devicePixelRatio || 1);
        const plumeX = logicalWidth * smokePlumes[plumeIndex];
        particlesRef.current.push(new Particle(logicalWidth, logicalHeight, plumeX));
      }

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particlesRef.current = particlesRef.current.filter(p => p.life > 0);
      for (const p of particlesRef.current) {
        p.update();
        p.draw(ctx);
      }

      animationFrameId.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    animate();

    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
      window.removeEventListener('resize', resizeCanvas);
      particlesRef.current = [];
    };
  }, [smokePlumes]); // Dependency array is now stable

  return (
    <div className="absolute inset-0 w-full h-full" style={{ mixBlendMode: 'screen' }}>
      <canvas ref={canvasRef} className="w-full h-full" />
    </div>
  );
};

export default IndustrialPulseShapes;
