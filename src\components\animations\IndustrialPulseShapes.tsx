'use client';

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { random } from '@/lib/random';

interface IndustrialPulseShapesProps {
  progress: number;
}

interface SmokeParticleProps {
  id: number;
  x: number;
  baseY: number;
  size: number;
  duration: number;
  delay: number;
  initialOpacity: number;
}

const SmokeParticle: React.FC<SmokeParticleProps> = ({ x, baseY, size, duration, delay, initialOpacity }) => {
  const travelY = -100 - size; // Travel from bottom to well off-screen
  const driftX = random(-20, 20);

  return (
    <motion.circle
      cx={x}
      cy={baseY}
      r={size}
      filter="url(#smoke-filter)"
      initial={{ opacity: 0, y: 0, x: 0 }}
      animate={{
        opacity: [0, initialOpacity, initialOpacity, 0],
        y: [0, travelY],
        x: [0, driftX],
        scale: [1, 1.5, 2]
      }}
      transition={{
        duration,
        delay,
        ease: 'linear',
        repeat: Infinity,
        repeatDelay: 2
      }}
    />
  );
};

const IndustrialPulseShapes: React.FC<IndustrialPulseShapesProps> = ({ progress }) => {
  const totalParticles = 60;
  const particleCount = Math.floor(progress * totalParticles);

  const particles = useMemo(() => {
    return Array.from({ length: totalParticles }).map((_, i) => ({
      id: i,
      x: random(0, 100),
      baseY: 110, // Start just below the viewport
      size: random(15, 40),
      duration: random(15, 30),
      delay: i * (totalParticles / 800), // Stagger the start times
      initialOpacity: random(0.1, 0.4),
    }));
  }, [totalParticles]);

  return (
    <div
      className="absolute inset-0 w-full h-full"
      style={{ mixBlendMode: 'screen' }}
    >
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        preserveAspectRatio="xMidYMid slice"
      >
        <defs>
          <filter id="smoke-filter">
            <motion.feTurbulence
              type="fractalNoise"
              baseFrequency="0.02 0.05"
              numOctaves="3"
              result="turbulence"
              animate={{ baseFrequency: ["0.02 0.05", "0.03 0.07", "0.02 0.05"] }}
              transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
            />
            <feDisplacementMap
              in2="turbulence"
              in="SourceGraphic"
              scale="10"
              xChannelSelector="R"
              yChannelSelector="G"
            />
            <feGaussianBlur stdDeviation="2" />
          </filter>
        </defs>
        <g fill="rgba(150, 150, 150, 1)">
          {particles.slice(0, particleCount).map(p => (
            <SmokeParticle key={p.id} {...p} />
          ))}
        </g>
      </svg>
    </div>
  );
};

export default IndustrialPulseShapes;
