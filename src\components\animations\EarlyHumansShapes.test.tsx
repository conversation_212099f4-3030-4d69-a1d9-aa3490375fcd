import React from 'react';
import { render, screen } from '@testing-library/react';
import EarlyHumansShapes from './EarlyHumansShapes';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    circle: ({ children, ...props }: any) => <circle {...props}>{children}</circle>,
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
  }),
}));

// Mock timers to prevent infinite loops in tests
jest.useFakeTimers();

describe('EarlyHumansShapes', () => {
  it('renders the container', () => {
    render(<EarlyHumansShapes progress={0.1} />);
    expect(screen.getByTestId('early-humans-container')).toBeInTheDocument();
  });

  it('does not show fire particles before ignition progress (36%)', () => {
    render(<EarlyHumansShapes progress={0.3} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'false');
  });

  it('shows fire particles after ignition progress (36%)', () => {
    render(<EarlyHumansShapes progress={0.5} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'true');
  });

  it('displays correct progress information', () => {
    render(<EarlyHumansShapes progress={0.75} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-progress', '0.750');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'true');
  });

  it('renders constellations (stars)', () => {
    render(<EarlyHumansShapes progress={0.1} />);
    const container = screen.getByTestId('early-humans-container');
    // Check that SVG elements are present (constellations)
    const svgElements = container.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);
  });

  it('calculates fire ignition progress correctly at boundary', () => {
    // Test the boundary condition - exactly at threshold should be false
    const { rerender } = render(<EarlyHumansShapes progress={0.36} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-show-fire', 'false');

    // Just above the threshold should be true
    rerender(<EarlyHumansShapes progress={0.37} />);
    expect(debugInfo).toHaveAttribute('data-show-fire', 'true');
  });

  it('renders correct number of fire, smoke, and sparkle particles when fire is active', () => {
    render(<EarlyHumansShapes progress={0.5} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-fire-particles-count', '120');
    expect(debugInfo).toHaveAttribute('data-smoke-particles-count', '25');
    expect(debugInfo).toHaveAttribute('data-sparkle-particles-count', '30');
  });

  it('renders no particles when fire is not active', () => {
    render(<EarlyHumansShapes progress={0.2} />);
    const debugInfo = screen.getByTestId('fire-debug');
    expect(debugInfo).toHaveAttribute('data-fire-particles-count', '0');
    expect(debugInfo).toHaveAttribute('data-smoke-particles-count', '0');
    expect(debugInfo).toHaveAttribute('data-sparkle-particles-count', '0');
  });

  describe('Fade-out Transitions', () => {
    it('starts fading out animations at 85% progress', () => {
      render(<EarlyHumansShapes progress={0.85} />);
      const debugInfo = screen.getByTestId('fire-debug');
      const fireOpacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      const constellationOpacity = parseFloat(debugInfo.getAttribute('data-constellation-opacity') || '1');

      // At exactly 85% progress, opacity should still be 1
      expect(fireOpacity).toBe(1);
      expect(constellationOpacity).toBe(1);
    });

    it('reduces opacity during fade-out period (85% to 90%)', () => {
      render(<EarlyHumansShapes progress={0.875} />); // 87.5% - halfway through fade-out
      const debugInfo = screen.getByTestId('fire-debug');
      const fireOpacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      const constellationOpacity = parseFloat(debugInfo.getAttribute('data-constellation-opacity') || '1');

      // At 87.5% progress (halfway through fade-out), opacity should be 0.5
      expect(fireOpacity).toBeCloseTo(0.5, 1);
      expect(constellationOpacity).toBeCloseTo(0.5, 1);
    });

    it('completely fades out animations at 90% progress', () => {
      render(<EarlyHumansShapes progress={0.9} />);
      const debugInfo = screen.getByTestId('fire-debug');
      const fireOpacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      const constellationOpacity = parseFloat(debugInfo.getAttribute('data-constellation-opacity') || '1');

      // At 90% progress, opacity should be 0
      expect(fireOpacity).toBe(0);
      expect(constellationOpacity).toBe(0);
    });

    it('keeps animations hidden after fade-out period', () => {
      render(<EarlyHumansShapes progress={0.95} />);
      const debugInfo = screen.getByTestId('fire-debug');
      const fireOpacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      const constellationOpacity = parseFloat(debugInfo.getAttribute('data-constellation-opacity') || '1');

      // After 90% progress, opacity should remain 0
      expect(fireOpacity).toBe(0);
      expect(constellationOpacity).toBe(0);
    });

    it('hides fire effects during fade-out period', () => {
      render(<EarlyHumansShapes progress={0.9} />);
      const debugInfo = screen.getByTestId('fire-debug');

      // Fire should be hidden during fade-out to prevent visual artifacts
      expect(debugInfo).toHaveAttribute('data-show-fire', 'false');
    });

    it('maintains visual continuity during transition', () => {
      // Test that the fade-out timing doesn't interfere with era transition
      const { rerender } = render(<EarlyHumansShapes progress={0.85} />);
      let debugInfo = screen.getByTestId('fire-debug');
      let opacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      expect(opacity).toBe(1);

      // Progress through fade-out
      rerender(<EarlyHumansShapes progress={0.88} />);
      debugInfo = screen.getByTestId('fire-debug');
      opacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      expect(opacity).toBeGreaterThan(0);
      expect(opacity).toBeLessThan(1);

      // Complete fade-out
      rerender(<EarlyHumansShapes progress={0.9} />);
      debugInfo = screen.getByTestId('fire-debug');
      opacity = parseFloat(debugInfo.getAttribute('data-fade-out-opacity') || '1');
      expect(opacity).toBe(0);
    });

    it('applies correct opacity styles to animation containers', () => {
      render(<EarlyHumansShapes progress={0.87} />);
      const container = screen.getByTestId('early-humans-container');

      // Check that opacity styles are applied to child elements
      const fireContainer = container.querySelector('div[style*="opacity"]');
      expect(fireContainer).toBeInTheDocument();
    });
  });
});
