'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useRef } from 'react';
import { Era, Section } from '@/types';
import PrimordialSoupShapes from './animations/PrimordialSoupShapes';
import MicrobialEdenShapes from './animations/MicrobialEdenShapes';
import CambrianBurstShapes from './animations/CambrianBurstShapes';
import FirstStepsOnLandShapes from './animations/FirstStepsOnLandShapes';
import AgeOfReptilesShapes from './animations/AgeOfReptilesShapes';
import ImpactAndAftermathShapes from './animations/ImpactAndAftermathShapes';
import MammalDawnShapes from './animations/MammalDawnShapes';
import EarlyHumansShapes from './animations/EarlyHumansShapes';
import AgricultureAndCitiesShapes from './animations/AgricultureAndCitiesShapes';
import IndustrialPulseShapes from './animations/IndustrialPulseShapes';

interface EvolutionEvent {
  id: number;
  x: number;
  y: number;
  size: number;
}

interface AnimatedShapesProps {
  currentEra: Era | null;
  currentSection: Section | null;
  currentTime: number;
  isPlaying: boolean;
  fadeOutDuration: number;
}

const AnimatedShapes: React.FC<AnimatedShapesProps> = ({ currentEra, currentSection, currentTime, isPlaying, fadeOutDuration }) => {
  const [evolutionQueue, setEvolutionQueue] = useState<EvolutionEvent[]>([]);
  const nextEvolutionId = useRef(0);

  const handleCellEvolve = (x: number, y: number, size: number) => {
    setEvolutionQueue((currentQueue: EvolutionEvent[]) => [
      ...currentQueue,
      { id: nextEvolutionId.current++, x, y, size },
    ]);
  };
  // Helper to determine the animation key, grouping eras that share visuals.
  const getAnimationKey = (eraId: string | undefined) => {
    if (eraId === 'microbialEden' || eraId === 'cambrianBurst') {
      return 'microbial-cambrian'; // Shared key for continuous animation
    }
    return eraId;
  };

  const renderEraShapes = () => {
    if (!isPlaying || !currentEra || !currentSection) return null;

    // Age of Reptiles has a unique, self-contained animation sequence.
    // We render it directly without the standard wrapper to allow its internal
    // useAnimation hooks to control the staggered fade-in.
    if (currentEra.id === 'ageOfReptiles') {
      return <AgeOfReptilesShapes currentTime={currentTime} />;
    }

    let shapeComponent = null;

    switch (currentEra.id) {
      case 'primordialSoup':
        const soupProgress = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <PrimordialSoupShapes progress={soupProgress} />;
        break;

      case 'microbialEden':
      case 'cambrianBurst': {
        const isCambrian = currentEra.id === 'cambrianBurst';
        let targetCellCount = 0;
        let evolutionChance = 0;

        if (isCambrian) {
          const cambrianStart = 90;
          const cambrianEnd = 140;
          const cambrianProgress = Math.max(0, (currentTime - cambrianStart) / (cambrianEnd - cambrianStart));
          targetCellCount = 100 + Math.floor(400 * Math.pow(cambrianProgress, 3));
          // Ramp up evolution chance from 0% to 5% over the first half of the era
          evolutionChance = Math.min(0.05, 0.1 * cambrianProgress);
        } else { // microbialEden
          const edenStart = 45;
          const edenEnd = 90;
          const edenProgress = (currentTime - edenStart) / (edenEnd - edenStart);
          targetCellCount = Math.min(
            100,
            Math.floor(10 * edenProgress + 90 * Math.pow(edenProgress, 8))
          );
        }

        shapeComponent = (
          <>
            <MicrobialEdenShapes
              targetCellCount={targetCellCount}
              onEvolve={isCambrian ? handleCellEvolve : undefined}
              evolutionChance={evolutionChance}
            />
            {isCambrian && (
              <CambrianBurstShapes
                evolutionQueue={evolutionQueue}
                onEvolutionComplete={() => setEvolutionQueue([])}
              />
            )}
          </>
        );
        break;
      }

      case 'firstStepsOnLand': {
        const landProgress = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <FirstStepsOnLandShapes progress={landProgress} />;
        break;
      }

      case 'impactAndAftermath': {
        const impactProgress = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <ImpactAndAftermathShapes progress={impactProgress} />;
        break;
      }

      case 'mammalDawn': {
        const progressInEra = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <MammalDawnShapes progress={progressInEra} />;
        break;
      }

      case 'earlyHumans': {
        const progressInEra = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <EarlyHumansShapes progress={progressInEra} />;
        break;
      }

      case 'agricultureAndCities': {
        const progressInEra = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <AgricultureAndCitiesShapes progress={progressInEra} />;
        break;
      }

      case 'industrialPulse': {
        const progressInEra = (currentTime - currentSection.start) / (currentSection.end - currentSection.start);
        shapeComponent = <IndustrialPulseShapes progress={progressInEra} />;
        break;
      }

      // NOTE: ageOfReptiles is handled above
      default:
        return null;
    }

    // This wrapper provides the standard cross-fade transition for all other eras.
    return (
      <motion.div
        key={getAnimationKey(currentEra.id)}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1, transition: { duration: 1.5, ease: 'easeInOut' } } }
        exit={{ opacity: 0, transition: { duration: fadeOutDuration, ease: 'easeInOut' } } }
        className="absolute inset-0"
      >
        {shapeComponent}
      </motion.div>
    );
  };

  return (
    <div className="pointer-events-none absolute inset-0">
      {/* Using mode='wait' ensures one animation finishes before the next starts */}
      <AnimatePresence mode="wait">{renderEraShapes()}</AnimatePresence>
    </div>
  );
};

export default AnimatedShapes;
