import { test, expect } from '@playwright/test';

const CHOICE_START_TIME = 480; // 8:00 minutes

test.describe('Audio-Visual Integration', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the experience page
    await page.goto('/experience');
  });

  test('should play correct audio after selecting dystopia choice', async ({ page }) => {
    // Start the audio player
    await page.getByRole('button', { name: /play/i }).click();

    // Wait for the seek function to be available and seek to choice time
    await page.waitForFunction(() => (window as any).seek);
    await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

    // Wait for choice buttons to appear
    const dystopiaButton = page.getByRole('button', { name: /dystopia/i });
    await expect(dystopiaButton).toBeVisible({ timeout: 5000 });

    // Listen for audio source changes
    const audioChanges: string[] = [];
    await page.exposeFunction('trackAudioChange', (src: string) => {
      audioChanges.push(src);
    });

    // Monitor audio element source changes
    await page.evaluate(() => {
      const audio = document.querySelector('audio');
      if (audio) {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
              const target = mutation.target as HTMLAudioElement;
              (window as any).trackAudioChange(target.src);
            }
          });
        });
        observer.observe(audio, { attributes: true });
        
        // Also track direct src property changes
        let lastSrc = audio.src;
        setInterval(() => {
          if (audio.src !== lastSrc) {
            lastSrc = audio.src;
            (window as any).trackAudioChange(audio.src);
          }
        }, 100);
      }
    });

    // Click dystopia button
    await dystopiaButton.click();

    // Wait a moment for audio changes to be processed
    await page.waitForTimeout(1000);

    // Verify that the audio source was changed to dystopia
    const currentAudioSrc = await page.evaluate(() => {
      const audio = document.querySelector('audio');
      return audio ? audio.src : null;
    });

    expect(currentAudioSrc).toContain('Timeline_Dystopia.mp3');

    // Verify choice corridor disappears
    await expect(page.getByTestId('choice-corridor')).not.toBeVisible();
  });

  test('should play correct audio after selecting utopia choice', async ({ page }) => {
    // Start the audio player
    await page.getByRole('button', { name: /play/i }).click();

    // Wait for the seek function to be available and seek to choice time
    await page.waitForFunction(() => (window as any).seek);
    await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

    // Wait for choice buttons to appear
    const utopiaButton = page.getByRole('button', { name: /utopia/i });
    await expect(utopiaButton).toBeVisible({ timeout: 5000 });

    // Click utopia button
    await utopiaButton.click();

    // Wait a moment for audio changes to be processed
    await page.waitForTimeout(1000);

    // Verify that the audio source was changed to utopia
    const currentAudioSrc = await page.evaluate(() => {
      const audio = document.querySelector('audio');
      return audio ? audio.src : null;
    });

    expect(currentAudioSrc).toContain('Timeline_Utopia.mp3');

    // Verify choice corridor disappears
    await expect(page.getByTestId('choice-corridor')).not.toBeVisible();
  });

  test('should handle rapid choice selection without audio conflicts', async ({ page }) => {
    // Start the audio player
    await page.getByRole('button', { name: /play/i }).click();

    // Seek to choice time
    await page.waitForFunction(() => (window as any).seek);
    await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

    // Wait for choice buttons to appear
    const utopiaButton = page.getByRole('button', { name: /utopia/i });
    const dystopiaButton = page.getByRole('button', { name: /dystopia/i });
    await expect(utopiaButton).toBeVisible({ timeout: 5000 });

    // Rapidly click both buttons (should only process first click)
    await utopiaButton.click();
    await dystopiaButton.click();
    await utopiaButton.click();

    // Wait for processing
    await page.waitForTimeout(1000);

    // Should have utopia audio (first click wins)
    const currentAudioSrc = await page.evaluate(() => {
      const audio = document.querySelector('audio');
      return audio ? audio.src : null;
    });

    expect(currentAudioSrc).toContain('Timeline_Utopia.mp3');
  });

  test('should log audio changes for debugging', async ({ page }) => {
    // Capture console logs
    const logs: string[] = [];
    page.on('console', (msg) => {
      if (msg.type() === 'log') {
        logs.push(msg.text());
      }
    });

    // Start the audio player
    await page.getByRole('button', { name: /play/i }).click();

    // Seek to choice time
    await page.waitForFunction(() => (window as any).seek);
    await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

    // Wait for choice buttons and click dystopia
    const dystopiaButton = page.getByRole('button', { name: /dystopia/i });
    await expect(dystopiaButton).toBeVisible({ timeout: 5000 });
    await dystopiaButton.click();

    // Wait for logs to be generated
    await page.waitForTimeout(1000);

    // Verify debug logs were generated
    const relevantLogs = logs.filter(log => 
      log.includes('Dystopia chosen') || 
      log.includes('Changing audio from')
    );

    expect(relevantLogs.length).toBeGreaterThan(0);
    expect(relevantLogs.some(log => log.includes('Dystopia chosen'))).toBe(true);
    expect(relevantLogs.some(log => log.includes('Timeline_Dystopia.mp3'))).toBe(true);
  });

  test('should maintain visual feedback consistency with audio selection', async ({ page }) => {
    // Start the audio player
    await page.getByRole('button', { name: /play/i }).click();

    // Seek to choice time
    await page.waitForFunction(() => (window as any).seek);
    await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

    // Wait for choice buttons to appear
    const dystopiaButton = page.getByRole('button', { name: /dystopia/i });
    await expect(dystopiaButton).toBeVisible({ timeout: 5000 });

    // Click dystopia
    await dystopiaButton.click();

    // Wait for state changes
    await page.waitForTimeout(1000);

    // Verify both audio and visual state are consistent
    const audioSrc = await page.evaluate(() => {
      const audio = document.querySelector('audio');
      return audio ? audio.src : null;
    });

    // Check that choice corridor is hidden (visual feedback)
    await expect(page.getByTestId('choice-corridor')).not.toBeVisible();

    // Check that audio is correct
    expect(audioSrc).toContain('Timeline_Dystopia.mp3');

    // Verify that the background or other visual elements reflect the dystopia choice
    // This would depend on your specific visual implementation
    const backgroundElement = page.locator('[data-testid="evolving-background"]');
    if (await backgroundElement.isVisible()) {
      // Add specific visual state checks here based on your implementation
      console.log('Background element is visible - visual state can be verified');
    }
  });

  test('should handle audio loading failures gracefully', async ({ page }) => {
    // Mock network to fail audio loading
    await page.route('**/Timeline_Dystopia.mp3', route => {
      route.abort('failed');
    });

    // Start the audio player
    await page.getByRole('button', { name: /play/i }).click();

    // Seek to choice time
    await page.waitForFunction(() => (window as any).seek);
    await page.evaluate((time) => (window as any).seek(time), CHOICE_START_TIME);

    // Wait for choice buttons and click dystopia
    const dystopiaButton = page.getByRole('button', { name: /dystopia/i });
    await expect(dystopiaButton).toBeVisible({ timeout: 5000 });
    await dystopiaButton.click();

    // Wait for processing
    await page.waitForTimeout(2000);

    // Application should not crash and choice corridor should still disappear
    await expect(page.getByTestId('choice-corridor')).not.toBeVisible();
    
    // Page should still be functional
    expect(await page.isVisible('body')).toBe(true);
  });
});
