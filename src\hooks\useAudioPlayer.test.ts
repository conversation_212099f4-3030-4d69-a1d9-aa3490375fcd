import { renderHook, act } from '@testing-library/react';
import { useAudioPlayer } from './useAudioPlayer';

const mockAudioInstance = {
  play: jest.fn().mockResolvedValue(undefined), // Return a resolved promise
  pause: jest.fn(),
  load: jest.fn(),
  addEventListener: jest.fn((event, cb) => {
    if (event === 'canplay') {
      // Immediately invoke the callback for 'canplay' to simulate audio loading
      cb();
    }
  }),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
  duration: 100,
  currentTime: 0,
  src: '',
  volume: 1,
  paused: false, // Add paused property for wasPlaying check
};

global.Audio = jest.fn().mockImplementation(() => mockAudioInstance);

describe('useAudioPlayer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset properties on the mock instance
    mockAudioInstance.currentTime = 0;
    mockAudioInstance.src = '';
    mockAudioInstance.volume = 1;
    mockAudioInstance.paused = false;
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    expect(result.current.isPlaying).toBe(false);
    expect(result.current.currentTime).toBe(0);
    expect(result.current.duration).toBe(0);
  });

  it('should call play', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    act(() => {
      result.current.play();
    });
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should call pause', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    act(() => {
      result.current.pause();
    });
    expect(mockAudioInstance.pause).toHaveBeenCalled();
  });

  it('should change source and preserve time', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // Simulate some playback time
    act(() => {
      mockAudioInstance.currentTime = 50;
    });

    act(() => {
      result.current.changeSource('new.mp3');
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    expect(mockAudioInstance.src).toContain('new.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    // The 'canplay' event listener in the mock will fire, setting the time
    expect(mockAudioInstance.currentTime).toBe(50);
    // It should also play after changing source by default
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should change source even when new source is the same as current', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // Set the source to the same value
    act(() => {
      mockAudioInstance.src = 'test.mp3';
    });

    act(() => {
      result.current.changeSource('test.mp3');
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    // Should still call load and update the source (with cache busting)
    expect(mockAudioInstance.src).toContain('test.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    expect(mockAudioInstance.play).toHaveBeenCalled();
  });

  it('should handle source change with playWhenReady=false', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    act(() => {
      mockAudioInstance.currentTime = 30;
      mockAudioInstance.paused = false; // Was playing
    });

    act(() => {
      result.current.changeSource('new.mp3', false);
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    expect(mockAudioInstance.src).toContain('new.mp3');
    expect(mockAudioInstance.load).toHaveBeenCalled();
    expect(mockAudioInstance.currentTime).toBe(30);
    // Should NOT play when playWhenReady is false
    expect(mockAudioInstance.play).not.toHaveBeenCalled();
  });

  it('should handle audio loading errors gracefully', async () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    // Store the error callback when addEventListener is called
    let errorCallback: (() => void) | null = null;
    mockAudioInstance.addEventListener = jest.fn((event, cb) => {
      if (event === 'error') {
        errorCallback = cb;
      }
    });

    act(() => {
      result.current.changeSource('invalid.mp3');
    });

    // Wait for the timeout in changeSource
    await new Promise(resolve => setTimeout(resolve, 60));

    // Manually trigger the error callback
    if (errorCallback) {
      act(() => {
        errorCallback();
      });
    }

    expect(consoleSpy).toHaveBeenCalledWith('[AudioPlayer] Failed to load audio source:', 'invalid.mp3');
    consoleSpy.mockRestore();
  });

  it('should update volume without re-initializing the player', () => {
    const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

    // The Audio constructor should be called once on initialization
    expect(global.Audio).toHaveBeenCalledTimes(1);
    expect(result.current.volume).toBe(1);
    expect(mockAudioInstance.volume).toBe(1);

    // Change the volume
    act(() => {
      result.current.setVolume(0.5);
    });

    // Assert that the volume state and the audio element's volume are updated
    expect(result.current.volume).toBe(0.5);
    expect(mockAudioInstance.volume).toBe(0.5);

    // Crucially, assert that the Audio constructor was not called again
    expect(global.Audio).toHaveBeenCalledTimes(1);
  });

  describe('One-shot playback system', () => {
    it('should start with hasStarted as false', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));
      expect(result.current.hasStarted).toBe(false);
    });

    it('should set hasStarted to true when play is called', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      act(() => {
        result.current.play();
      });

      expect(result.current.hasStarted).toBe(true);
    });

    it('should not allow pause after playback has started', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Start playback
      act(() => {
        result.current.play();
      });

      expect(result.current.hasStarted).toBe(true);

      // Try to pause - should not work
      act(() => {
        result.current.pause();
      });

      expect(mockAudioInstance.pause).not.toHaveBeenCalled();
    });

    it('should allow pause before playback has started', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Pause before starting - should work
      act(() => {
        result.current.pause();
      });

      expect(mockAudioInstance.pause).toHaveBeenCalled();
    });

    it('should calculate remaining time correctly', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Set duration and current time
      act(() => {
        mockAudioInstance.duration = 600; // 10 minutes
        mockAudioInstance.currentTime = 100; // 1 minute 40 seconds
        mockAudioInstance.dispatchEvent(new Event('loadedmetadata'));
        mockAudioInstance.dispatchEvent(new Event('timeupdate'));
      });

      expect(result.current.remainingTime).toBe(500); // 8 minutes 20 seconds
    });

    it('should handle zero remaining time', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Set current time equal to duration
      act(() => {
        mockAudioInstance.duration = 600;
        mockAudioInstance.currentTime = 600;
        mockAudioInstance.dispatchEvent(new Event('loadedmetadata'));
        mockAudioInstance.dispatchEvent(new Event('timeupdate'));
      });

      expect(result.current.remainingTime).toBe(0);
    });

    it('should handle remaining time when current time exceeds duration', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Set current time greater than duration
      act(() => {
        mockAudioInstance.duration = 600;
        mockAudioInstance.currentTime = 700;
        mockAudioInstance.dispatchEvent(new Event('loadedmetadata'));
        mockAudioInstance.dispatchEvent(new Event('timeupdate'));
      });

      expect(result.current.remainingTime).toBe(0);
    });

    it('should reset all state when reset is called', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Start playback and set some state
      act(() => {
        mockAudioInstance.currentTime = 100;
        mockAudioInstance.duration = 600;
        result.current.play();
        mockAudioInstance.dispatchEvent(new Event('loadedmetadata'));
        mockAudioInstance.dispatchEvent(new Event('timeupdate'));
      });

      expect(result.current.hasStarted).toBe(true);
      expect(result.current.currentTime).toBe(100);

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.hasStarted).toBe(false);
      expect(result.current.currentTime).toBe(0);
      expect(result.current.remainingTime).toBe(0);
      expect(result.current.isPlaying).toBe(false);
      expect(mockAudioInstance.pause).toHaveBeenCalled();
      expect(mockAudioInstance.currentTime).toBe(0);
    });

    it('should maintain hasStarted state across source changes', () => {
      const { result } = renderHook(() => useAudioPlayer({ initialSrc: 'test.mp3' }));

      // Start playback
      act(() => {
        result.current.play();
      });

      expect(result.current.hasStarted).toBe(true);

      // Change source
      act(() => {
        result.current.changeSource('new.mp3');
      });

      // Wait for the timeout in changeSource
      setTimeout(() => {
        // hasStarted should remain true
        expect(result.current.hasStarted).toBe(true);
      }, 60);
    });
  });
});


