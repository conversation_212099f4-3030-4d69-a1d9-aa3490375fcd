import { render, screen } from '@testing-library/react';
import AgricultureAndCitiesShapes from './AgricultureAndCitiesShapes';

// Mock framer-motion to avoid animation complexity in tests
jest.mock('framer-motion', () => ({
  motion: {
    path: ({ children, style, ...props }: any) => <path style={style} {...props}>{children}</path>,
    div: ({ children, style, ...props }: any) => <div style={style} {...props}>{children}</div>,
  },
  useIsPresent: () => true,
}));

describe('AgricultureAndCitiesShapes', () => {
  it('renders the container with correct test id', () => {
    render(<AgricultureAndCitiesShapes progress={0.5} />);
    expect(screen.getByTestId('agriculture-cities-container')).toBeInTheDocument();
  });

  it('applies full opacity before fade-out period', () => {
    render(<AgricultureAndCitiesShapes progress={0.8} />);
    const debugInfo = screen.getByTestId('agriculture-debug');
    const animationOpacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '0');

    expect(animationOpacity).toBe(1);
  });

  it('starts fading out animations at 85% progress', () => {
    render(<AgricultureAndCitiesShapes progress={0.85} />);
    const debugInfo = screen.getByTestId('agriculture-debug');
    const animationOpacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '0');

    // At exactly 85% progress, opacity should still be 1
    expect(animationOpacity).toBe(1);
  });

  it('reduces opacity during fade-out period (85% to 89%)', () => {
    render(<AgricultureAndCitiesShapes progress={0.87} />); // 87% - halfway through fade-out
    const debugInfo = screen.getByTestId('agriculture-debug');
    const animationOpacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');

    // At 87% progress (halfway through fade-out), opacity should be 0.5
    expect(animationOpacity).toBeCloseTo(0.5, 1);
  });

  it('completely fades out animations at 89% progress', () => {
    render(<AgricultureAndCitiesShapes progress={0.89} />);
    const debugInfo = screen.getByTestId('agriculture-debug');
    const animationOpacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');

    // At 89% progress, opacity should be 0
    expect(animationOpacity).toBe(0);
  });

  it('keeps animations hidden after fade-out period', () => {
    render(<AgricultureAndCitiesShapes progress={0.95} />);
    const debugInfo = screen.getByTestId('agriculture-debug');
    const animationOpacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');

    // After 89% progress, opacity should remain 0
    expect(animationOpacity).toBe(0);
  });

  it('applies opacity style to the container', () => {
    render(<AgricultureAndCitiesShapes progress={0.87} />);
    const container = screen.getByTestId('agriculture-cities-container');

    // Check that the container has an opacity style applied
    expect(container).toHaveStyle('opacity: 0.5');
  });

  it('provides debug information for testing', () => {
    const progress = 0.6;
    render(<AgricultureAndCitiesShapes progress={progress} />);
    const debugInfo = screen.getByTestId('agriculture-debug');

    expect(debugInfo).toHaveAttribute('data-progress', progress.toFixed(3));
    expect(debugInfo).toHaveAttribute('data-animation-opacity');
    expect(debugInfo).toHaveAttribute('data-is-present', 'true');
    expect(debugInfo).toHaveAttribute('data-settlements-count');
    expect(debugInfo).toHaveAttribute('data-paths-count');
  });

  it('shows settlements and paths based on progress', () => {
    render(<AgricultureAndCitiesShapes progress={0.5} />);
    const debugInfo = screen.getByTestId('agriculture-debug');

    const settlementsCount = parseInt(debugInfo.getAttribute('data-settlements-count') || '0');
    const pathsCount = parseInt(debugInfo.getAttribute('data-paths-count') || '0');

    expect(settlementsCount).toBeGreaterThan(0);
    expect(pathsCount).toBeGreaterThan(0);
  });

  it('maintains visual continuity during transition', () => {
    // Test that the fade-out timing doesn't interfere with era transition
    const { rerender } = render(<AgricultureAndCitiesShapes progress={0.85} />);
    let debugInfo = screen.getByTestId('agriculture-debug');
    let opacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');
    expect(opacity).toBe(1);

    // Progress through fade-out
    rerender(<AgricultureAndCitiesShapes progress={0.87} />);
    debugInfo = screen.getByTestId('agriculture-debug');
    opacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');
    expect(opacity).toBeCloseTo(0.5, 1);

    // Complete fade-out
    rerender(<AgricultureAndCitiesShapes progress={0.89} />);
    debugInfo = screen.getByTestId('agriculture-debug');
    opacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');
    expect(opacity).toBe(0);
  });

  it('renders SVG elements for settlements and paths', () => {
    render(<AgricultureAndCitiesShapes progress={0.5} />);
    const container = screen.getByTestId('agriculture-cities-container');

    // Check that SVG elements are present
    const svgElement = container.querySelector('svg');
    expect(svgElement).toBeInTheDocument();

    // Check for filter definitions
    const filterElement = container.querySelector('filter#glow');
    expect(filterElement).toBeInTheDocument();
  });

  describe('Settlement and Path Animation', () => {
    it('shows more settlements as progress increases', () => {
      const { rerender } = render(<AgricultureAndCitiesShapes progress={0.2} />);
      let debugInfo = screen.getByTestId('agriculture-debug');
      const earlySettlements = parseInt(debugInfo.getAttribute('data-settlements-count') || '0');

      rerender(<AgricultureAndCitiesShapes progress={0.8} />);
      debugInfo = screen.getByTestId('agriculture-debug');
      const lateSettlements = parseInt(debugInfo.getAttribute('data-settlements-count') || '0');

      expect(lateSettlements).toBeGreaterThan(earlySettlements);
    });

    it('shows paths after settlements start appearing', () => {
      // Early progress should have settlements but few/no paths
      const { rerender } = render(<AgricultureAndCitiesShapes progress={0.1} />);
      let debugInfo = screen.getByTestId('agriculture-debug');
      const earlyPaths = parseInt(debugInfo.getAttribute('data-paths-count') || '0');

      // Later progress should have more paths
      rerender(<AgricultureAndCitiesShapes progress={0.5} />);
      debugInfo = screen.getByTestId('agriculture-debug');
      const latePaths = parseInt(debugInfo.getAttribute('data-paths-count') || '0');

      expect(latePaths).toBeGreaterThanOrEqual(earlyPaths);
    });
  });

  describe('Fade-out Timing Coordination', () => {
    it('calculates fade-out timing correctly for era transition', () => {
      // Agriculture & Cities era ends at 405s with 5s fade-out
      // This means fade-out starts at ~400s (89% of 45s era duration)
      const fadeOutStartProgress = 0.85;
      const fadeOutEndProgress = 0.89;

      expect(fadeOutEndProgress - fadeOutStartProgress).toBeCloseTo(0.04, 2);
    });

    it('ensures animations are fully faded before era transition', () => {
      // At 89% progress, animations should be completely hidden
      render(<AgricultureAndCitiesShapes progress={0.89} />);
      const container = screen.getByTestId('agriculture-cities-container');

      expect(container).toHaveStyle('opacity: 0');
    });

    it('prevents visual artifacts during era transition', () => {
      // Test that opacity reaches 0 before the era transition begins
      const { rerender } = render(<AgricultureAndCitiesShapes progress={0.88} />);
      let debugInfo = screen.getByTestId('agriculture-debug');
      let opacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');
      expect(opacity).toBeGreaterThan(0);

      rerender(<AgricultureAndCitiesShapes progress={0.89} />);
      debugInfo = screen.getByTestId('agriculture-debug');
      opacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '1');
      expect(opacity).toBe(0);
    });
  });

  describe('useIsPresent Integration', () => {
    it('handles component exit state correctly', () => {
      // Test that the component respects the isPresent state
      // This is tested indirectly through the opacity calculation
      render(<AgricultureAndCitiesShapes progress={0.5} />);
      const debugInfo = screen.getByTestId('agriculture-debug');

      // When component is present, it should show the expected opacity
      expect(debugInfo).toHaveAttribute('data-is-present', 'true');

      const animationOpacity = parseFloat(debugInfo.getAttribute('data-animation-opacity') || '0');
      expect(animationOpacity).toBe(1); // Should be fully visible at 50% progress
    });
  });
});
