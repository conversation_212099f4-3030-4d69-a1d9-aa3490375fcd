'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence, motionValue, animate, MotionValue } from 'framer-motion';

// Represents the state for a single cell, using MotionValues for coordinates.
interface CellData {
  id: number | string;
  x: MotionValue<number>;
  y: MotionValue<number>;
  size: number;
  color: string;
  animationState: {
    isRunning: boolean;
  };
}

interface CellComponentProps {
  cell: CellData;
}

// The visual component for a single cell, memoized for performance.
const CellComponent: React.FC<CellComponentProps> = React.memo(({ cell }) => {
  return (
    <motion.div
      className="absolute rounded-full bg-cyan-200/50 border-2 border-cyan-100/80"
      style={{
        width: cell.size,
        height: cell.size,
        x: cell.x,
        y: cell.y,
        boxShadow: '0 0 10px #67e8f9, 0 0 20px #67e8f9',
        transform: 'translate(-50%, -50%)', // Center the circle
      }}
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 0.7, scale: 1 }}
      transition={{ duration: 1.5, ease: 'easeOut' }}
      exit={{
        opacity: 0,
        scale: 0.5,
        transition: { duration: 2, ease: 'easeIn' },
      }}
    />
  );
});
CellComponent.displayName = 'CellComponent';

interface MicrobialEdenShapesProps {
  targetCellCount: number;
  onEvolve?: (x: number, y: number, size: number) => void;
  evolutionChance?: number;
}

// Main component to orchestrate the entire animation
const MicrobialEdenShapes: React.FC<MicrobialEdenShapesProps> = ({ targetCellCount, onEvolve, evolutionChance = 0 }) => {
  const [cells, setCells] = useState<CellData[]>([]);
  const cellsRef = useRef(cells);
  cellsRef.current = cells;
  const windowSize = useRef({ width: 0, height: 0 });
  const nextId = useRef(0);

  // Helper to create a new cell with random properties
  const createCell = useCallback((windowSize: { width: number; height: number }): CellData => {
    const size = Math.random() * 20 + 10; // Random size between 10 and 30
    return {
      id: String(nextId.current++),
      x: motionValue(Math.random() * windowSize.width),
      y: motionValue(Math.random() * windowSize.height),
      size,
      color: `hsl(${Math.random() * 360}, 70%, 50%)`,
      animationState: { isRunning: true },
    };
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      windowSize.current = { width: window.innerWidth, height: window.innerHeight };
    }
  }, []);

  // Starts a continuous, gentle, swirling drift animation for a cell
  const startDrift = useCallback((cell: CellData) => {
    let currentAngle = Math.random() * 2 * Math.PI;

    const animateWander = async () => {
      if (!cell.animationState.isRunning) return;

      // Adjust angle slightly for a gentle curve
      currentAngle += (Math.random() - 0.5) * 0.4; // ~11 degrees change

      // Move in smaller, more frequent steps for a smoother path
      const stepDistance = Math.random() * 50 + 25; // 25-75px
      const duration = Math.random() * 4 + 6; // 6-10s, slow and gentle

      let nextX = cell.x.get() + Math.cos(currentAngle) * stepDistance;
      let nextY = cell.y.get() + Math.sin(currentAngle) * stepDistance;

      // Bounce off walls
      const margin = cell.size;
      if (nextX < margin || nextX > windowSize.current.width - margin) {
        nextX = Math.max(margin, Math.min(windowSize.current.width - margin, nextX));
        currentAngle = Math.PI - currentAngle;
      }
      if (nextY < margin || nextY > windowSize.current.height - margin) {
        nextY = Math.max(margin, Math.min(windowSize.current.height - margin, nextY));
        currentAngle = -currentAngle;
      }

      try {
        await Promise.all([
          animate(cell.x, nextX, { duration, ease: 'linear' }),
          animate(cell.y, nextY, { duration, ease: 'linear' }),
        ]);
      } catch (e) {
        return; // Animation interrupted
      }

      if (cell.animationState.isRunning) {
        animateWander();
      }
    };

    animateWander();
  }, []);

  // Effect to handle cell creation and division
  useEffect(() => {
    const timer = setInterval(() => {
      setCells(currentCells => {
        let updatedCells = [...currentCells];

        // --- Evolution Logic ---
        if (onEvolve && updatedCells.length > 15 && Math.random() < evolutionChance) {
          const cellToEvolve = updatedCells[updatedCells.length - 1];
          if (cellToEvolve) {
            cellToEvolve.animationState.isRunning = false;
            onEvolve(cellToEvolve.x.get(), cellToEvolve.y.get(), cellToEvolve.size);
            updatedCells.pop();
            return updatedCells; // Evolve and exit this tick
          }
        }

        // --- Culling/Spawning Logic ---
        if (updatedCells.length > targetCellCount) {
          // Gradual culling: remove one cell per interval for smoother animation
          const cellToRemove = updatedCells[0];
          if (cellToRemove) {
            cellToRemove.animationState.isRunning = false;
          }
          updatedCells = updatedCells.slice(1);
        } else if (updatedCells.length < targetCellCount) {
          const newCell = createCell(windowSize.current);
          startDrift(newCell);
          updatedCells.push(newCell);
        }

        return updatedCells;
      });
    }, 50); // Division check interval

    // --- Cleanup Function ---
    return () => {
      clearInterval(timer);
      // *** DEFINITIVE FIX: Use a ref to get the latest cells and stop their animation loops ***
      cellsRef.current.forEach(c => (c.animationState.isRunning = false));
    };
  }, [targetCellCount, onEvolve, evolutionChance, startDrift, createCell]);



  return (
    <div className="absolute inset-0 w-full h-full overflow-hidden transform-gpu">
      <AnimatePresence>
        {cells.map(cell => (
          <CellComponent key={cell.id} cell={cell} />
        ))}
      </AnimatePresence>
    </div>
  );
};

export default MicrobialEdenShapes;


