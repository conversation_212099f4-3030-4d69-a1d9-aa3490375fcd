import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import AgeOfReptilesShapes from './AgeOfReptilesShapes';

// Mock child components to isolate the test
jest.mock('./DistantBirds', () => () => <div data-testid="distant-birds-mock" />);

// Mock framer-motion with stable, typed placeholders
const mockStart = jest.fn().mockResolvedValue(undefined);
jest.mock('framer-motion', () => ({
  motion: {
    // Provide a typed mock for motion.div using React.forwardRef
    div: React.forwardRef<HTMLDivElement, React.PropsWithChildren<{}>>((props, ref) => (
      <div {...props} ref={ref}>
        {props.children}
      </div>
    )),
  },
  useAnimation: () => ({
    start: mockStart,
  }),
}));

describe('AgeOfReptilesShapes', () => {
  beforeEach(() => {
    mockStart.mockClear();
    jest.useRealTimers();
  });

  it('renders the component and its mocked children', () => {
    render(<AgeOfReptilesShapes currentTime={0} />);
    expect(screen.getByTestId('distant-birds-mock')).toBeInTheDocument();
  });

  it('starts the animation sequence on mount', async () => {
    jest.useFakeTimers();
    render(<AgeOfReptilesShapes currentTime={0} />);

    // Sun animation is called immediately
    await waitFor(() => {
      expect(mockStart).toHaveBeenCalledWith({
        opacity: 1,
        transition: { duration: 2, ease: 'easeInOut' },
      });
    });

    expect(mockStart).toHaveBeenCalledTimes(1);

    // Advance timers to trigger the content animation
    jest.advanceTimersByTime(1000);

    // Content animation is called after the delay
    await waitFor(() => {
      expect(mockStart).toHaveBeenCalledTimes(2);
      expect(mockStart).toHaveBeenLastCalledWith({
        opacity: 1,
        transition: { duration: 10, ease: 'easeInOut' },
      });
    });
  });
});
