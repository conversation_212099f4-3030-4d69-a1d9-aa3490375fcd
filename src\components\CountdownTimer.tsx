'use client';

import React from 'react';
import { Clock } from 'lucide-react';
import { formatCountdown } from '@/utils/timeFormat';
import { cn } from '@/lib/utils';

interface CountdownTimerProps {
  remainingTime: number;
  className?: string;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  remainingTime,
  className
}) => {
  const formattedTime = formatCountdown(remainingTime);

  // Consistent styling throughout countdown - maintains orange/neutral appearance
  const getConsistentStyle = () => {
    return "text-white border-gray-400/30 bg-white/10";
  };

  return (
    <div
      className={cn(
        'rounded-md p-px bg-gradient-to-b from-gray-300/25 to-gray-600/20',
        className
      )}
    >
      <div
        className={cn(
          'flex items-center backdrop-blur-sm rounded-[6px] h-10 px-4 transition-all duration-300 ease-in-out border',
          getConsistentStyle()
        )}
      >
        <Clock className="mr-2 h-4 w-4" />
        <span className="font-mono text-sm font-medium whitespace-nowrap">
          {formattedTime}
        </span>
      </div>
    </div>
  );
};

export default CountdownTimer;
