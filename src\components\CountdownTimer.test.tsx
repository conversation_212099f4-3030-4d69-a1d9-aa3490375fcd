import { render, screen } from '@testing-library/react';
import CountdownTimer from './CountdownTimer';

// Mock the lucide-react Clock icon
jest.mock('lucide-react', () => ({
  Clock: ({ className }: { className?: string }) => (
    <div data-testid="clock-icon" className={className} />
  ),
}));

describe('CountdownTimer', () => {
  it('renders with correct countdown text', () => {
    render(<CountdownTimer remainingTime={542} />);
    expect(screen.getByText('9:02 remaining')).toBeInTheDocument();
  });

  it('displays clock icon', () => {
    render(<CountdownTimer remainingTime={300} />);
    expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <CountdownTimer remainingTime={100} className="custom-class" />
    );
    expect(container.firstChild).toHaveClass('custom-class');
  });

  describe('Urgency styling', () => {
    it('applies normal styling for times over 5 minutes', () => {
      render(<CountdownTimer remainingTime={400} />);
      const timerElement = screen.getByText('6:40 remaining').closest('div');
      expect(timerElement).toHaveClass('text-white', 'border-gray-400/30', 'bg-white/10');
    });

    it('applies warning styling for times between 1-5 minutes', () => {
      render(<CountdownTimer remainingTime={180} />);
      const timerElement = screen.getByText('3:00 remaining').closest('div');
      expect(timerElement).toHaveClass('text-yellow-400', 'border-yellow-400/30', 'bg-yellow-500/10');
    });

    it('applies urgent styling for times under 1 minute', () => {
      render(<CountdownTimer remainingTime={30} />);
      const timerElement = screen.getByText('30s remaining').closest('div');
      expect(timerElement).toHaveClass('text-red-400', 'border-red-400/30', 'bg-red-500/10');
    });

    it('applies urgent styling at exactly 60 seconds', () => {
      render(<CountdownTimer remainingTime={60} />);
      const timerElement = screen.getByText('1:00 remaining').closest('div');
      expect(timerElement).toHaveClass('text-red-400', 'border-red-400/30', 'bg-red-500/10');
    });

    it('applies warning styling at exactly 300 seconds', () => {
      render(<CountdownTimer remainingTime={300} />);
      const timerElement = screen.getByText('5:00 remaining').closest('div');
      expect(timerElement).toHaveClass('text-yellow-400', 'border-yellow-400/30', 'bg-yellow-500/10');
    });
  });

  describe('Time formatting', () => {
    it('formats seconds correctly', () => {
      render(<CountdownTimer remainingTime={45} />);
      expect(screen.getByText('45s remaining')).toBeInTheDocument();
    });

    it('formats minutes and seconds correctly', () => {
      render(<CountdownTimer remainingTime={125} />);
      expect(screen.getByText('2:05 remaining')).toBeInTheDocument();
    });

    it('handles zero time', () => {
      render(<CountdownTimer remainingTime={0} />);
      expect(screen.getByText('0s remaining')).toBeInTheDocument();
    });

    it('handles negative time', () => {
      render(<CountdownTimer remainingTime={-10} />);
      expect(screen.getByText('0s remaining')).toBeInTheDocument();
    });
  });

  describe('Visual structure', () => {
    it('has correct container structure', () => {
      const { container } = render(<CountdownTimer remainingTime={100} />);
      
      // Outer gradient container
      const outerContainer = container.firstChild as HTMLElement;
      expect(outerContainer).toHaveClass('rounded-md', 'p-px', 'bg-gradient-to-b');
      
      // Inner backdrop blur container
      const innerContainer = outerContainer.firstChild as HTMLElement;
      expect(innerContainer).toHaveClass('backdrop-blur-sm', 'rounded-[6px]', 'h-10', 'px-4');
    });

    it('uses monospace font for time display', () => {
      render(<CountdownTimer remainingTime={100} />);
      const timeText = screen.getByText('1:40 remaining');
      expect(timeText).toHaveClass('font-mono');
    });

    it('prevents text wrapping', () => {
      render(<CountdownTimer remainingTime={100} />);
      const timeText = screen.getByText('1:40 remaining');
      expect(timeText).toHaveClass('whitespace-nowrap');
    });

    it('has smooth transitions', () => {
      render(<CountdownTimer remainingTime={100} />);
      const innerContainer = screen.getByText('1:40 remaining').closest('div');
      expect(innerContainer).toHaveClass('transition-all', 'duration-300', 'ease-in-out');
    });
  });

  describe('Accessibility', () => {
    it('provides meaningful text content', () => {
      render(<CountdownTimer remainingTime={542} />);
      expect(screen.getByText('9:02 remaining')).toBeInTheDocument();
    });

    it('includes clock icon for visual context', () => {
      render(<CountdownTimer remainingTime={100} />);
      const clockIcon = screen.getByTestId('clock-icon');
      expect(clockIcon).toHaveClass('mr-2', 'h-4', 'w-4');
    });
  });

  describe('Edge cases', () => {
    it('handles very large times', () => {
      render(<CountdownTimer remainingTime={3661} />);
      expect(screen.getByText('61:01 remaining')).toBeInTheDocument();
    });

    it('handles decimal seconds', () => {
      render(<CountdownTimer remainingTime={59.9} />);
      expect(screen.getByText('1:00 remaining')).toBeInTheDocument();
    });

    it('maintains styling consistency across different times', () => {
      const { rerender } = render(<CountdownTimer remainingTime={400} />);
      let timerElement = screen.getByText('6:40 remaining').closest('div');
      expect(timerElement).toHaveClass('text-white');

      rerender(<CountdownTimer remainingTime={200} />);
      timerElement = screen.getByText('3:20 remaining').closest('div');
      expect(timerElement).toHaveClass('text-yellow-400');

      rerender(<CountdownTimer remainingTime={30} />);
      timerElement = screen.getByText('30s remaining').closest('div');
      expect(timerElement).toHaveClass('text-red-400');
    });
  });
});
