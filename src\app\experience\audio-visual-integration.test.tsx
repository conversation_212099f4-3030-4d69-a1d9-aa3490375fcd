import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ExperiencePage from './page';

// Mock the global fetch function
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({}),
  })
) as jest.Mock;

import { useAudioPlayer } from '@/hooks/useAudioPlayer';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn().mockReturnValue(null),
  }),
}));

// Mock the custom hook and child components
jest.mock('@/hooks/useAudioPlayer');

jest.mock('@/components/Timeline', () => {
  const MockTimeline = () => <div data-testid="timeline" />;
  MockTimeline.displayName = 'MockTimeline';
  return MockTimeline;
});

jest.mock('@/components/TimedTextDisplay', () => {
  const MockTimedTextDisplay = () => <div data-testid="timed-text-display" />;
  MockTimedTextDisplay.displayName = 'MockTimedTextDisplay';
  return MockTimedTextDisplay;
});

jest.mock('@/components/ChoiceCorridor', () => {
  const MockChoiceCorridor = jest.fn(({ onSelectUtopia, onSelectDystopia }) => (
    <div data-testid="choice-corridor">
      <button onClick={onSelectUtopia}>Choose Utopia</button>
      <button onClick={onSelectDystopia}>Choose Dystopia</button>
    </div>
  ));
  return Object.assign(MockChoiceCorridor, { displayName: 'MockChoiceCorridor' });
});

describe('ExperiencePage Audio-Visual Integration', () => {
  const mockPlay = jest.fn();
  const mockPause = jest.fn();
  const mockChangeSource = jest.fn();
  const mockSeek = jest.fn();

  const setupMockHook = (currentTime: number, initialSrc?: string) => {
    (useAudioPlayer as jest.Mock).mockReturnValue({
      isPlaying: true,
      currentTime,
      duration: 600,
      volume: 1,
      setVolume: jest.fn(),
      play: mockPlay,
      pause: mockPause,
      changeSource: mockChangeSource,
      seek: mockSeek,
    });
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Clear console.log calls to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Choice Selection and Audio Synchronization', () => {
    it('should always trigger audio change when selecting utopia', () => {
      setupMockHook(490); // Within choice window
      render(<ExperiencePage />);

      fireEvent.click(screen.getByRole('button', { name: /choose utopia/i }));

      expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Utopia.mp3');
      expect(mockChangeSource).toHaveBeenCalledTimes(1);
    });

    it('should always trigger audio change when selecting dystopia', () => {
      setupMockHook(490); // Within choice window
      render(<ExperiencePage />);

      fireEvent.click(screen.getByRole('button', { name: /choose dystopia/i }));

      expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Dystopia.mp3');
      expect(mockChangeSource).toHaveBeenCalledTimes(1);
    });

    it('should log audio changes for debugging', () => {
      const consoleSpy = jest.spyOn(console, 'log');
      setupMockHook(490);
      render(<ExperiencePage />);

      fireEvent.click(screen.getByRole('button', { name: /choose utopia/i }));

      expect(consoleSpy).toHaveBeenCalledWith('Utopia chosen');
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Changing audio from')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Timeline_Utopia.mp3')
      );
    });

    it('should prevent multiple choice selections', () => {
      setupMockHook(490);
      render(<ExperiencePage />);

      const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });

      // First click should work
      fireEvent.click(utopiaButton);
      expect(mockChangeSource).toHaveBeenCalledTimes(1);

      // Try to click the same button again - should be ignored due to choiceMade state
      fireEvent.click(utopiaButton);
      expect(mockChangeSource).toHaveBeenCalledTimes(1); // Still only 1 call
    });

    it('should hide choice corridor after selection', () => {
      setupMockHook(490);
      const { rerender } = render(<ExperiencePage />);

      // Choice corridor should be visible initially
      expect(screen.getByTestId('choice-corridor')).toBeInTheDocument();

      // Make a choice
      fireEvent.click(screen.getByRole('button', { name: /choose utopia/i }));

      // Re-render to simulate state update
      rerender(<ExperiencePage />);

      // Choice corridor should be hidden
      expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
    });
  });

  describe('Audio State Consistency', () => {
    it('should maintain audio consistency across different choice scenarios', async () => {
      // Test multiple scenarios to ensure audio state is always correct
      const scenarios = [
        { choice: 'utopia', expectedAudio: '/audio/Timeline_Utopia.mp3' },
        { choice: 'dystopia', expectedAudio: '/audio/Timeline_Dystopia.mp3' },
      ];

      for (const scenario of scenarios) {
        jest.clearAllMocks();
        setupMockHook(490);
        const { unmount } = render(<ExperiencePage />);

        fireEvent.click(
          screen.getByRole('button', {
            name: new RegExp(`choose ${scenario.choice}`, 'i')
          })
        );

        expect(mockChangeSource).toHaveBeenCalledWith(scenario.expectedAudio);
        unmount();
      }
    });

    it('should handle rapid successive choice attempts gracefully', () => {
      setupMockHook(490);
      render(<ExperiencePage />);

      // Simulate rapid clicking
      const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });

      fireEvent.click(utopiaButton);
      fireEvent.click(utopiaButton);
      fireEvent.click(utopiaButton);

      // Should only process the first click
      expect(mockChangeSource).toHaveBeenCalledTimes(1);
      expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Utopia.mp3');
    });
  });

  describe('Automatic Choice Selection', () => {
    it('should automatically select and change audio when choice window expires', async () => {
      const CHOICE_END_TIME = 540;
      // Mock Math.random to force utopia selection
      const randomSpy = jest.spyOn(Math, 'random').mockReturnValue(0.3);

      setupMockHook(CHOICE_END_TIME + 1);
      render(<ExperiencePage />);

      await waitFor(() => {
        expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Utopia.mp3');
      });

      randomSpy.mockRestore();
    });

    it('should automatically select dystopia when random value is high', async () => {
      const CHOICE_END_TIME = 540;
      // Mock Math.random to force dystopia selection
      const randomSpy = jest.spyOn(Math, 'random').mockReturnValue(0.7);

      setupMockHook(CHOICE_END_TIME + 1);
      render(<ExperiencePage />);

      await waitFor(() => {
        expect(mockChangeSource).toHaveBeenCalledWith('/audio/Timeline_Dystopia.mp3');
      }, { timeout: 2000 });

      randomSpy.mockRestore();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle audio change failures gracefully', () => {
      const mockChangeSourceWithError = jest.fn(() => {
        throw new Error('Audio change failed');
      });

      (useAudioPlayer as jest.Mock).mockReturnValue({
        isPlaying: true,
        currentTime: 490,
        duration: 600,
        volume: 1,
        setVolume: jest.fn(),
        play: mockPlay,
        pause: mockPause,
        changeSource: mockChangeSourceWithError,
        seek: mockSeek,
      });

      // Should not crash the application even when audio change fails
      const { container } = render(<ExperiencePage />);

      // The component should render successfully
      expect(container).toBeInTheDocument();

      // Clicking should not crash the app (error is caught and logged)
      const utopiaButton = screen.getByRole('button', { name: /choose utopia/i });
      expect(() => fireEvent.click(utopiaButton)).not.toThrow();

      // The error should be logged but app continues to function
      expect(mockChangeSourceWithError).toHaveBeenCalled();
    });

    it('should not show choice corridor outside the choice window', () => {
      setupMockHook(100); // Before choice window
      render(<ExperiencePage />);
      expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();

      jest.clearAllMocks();
      setupMockHook(600); // After choice window
      render(<ExperiencePage />);
      expect(screen.queryByTestId('choice-corridor')).not.toBeInTheDocument();
    });
  });
});
