import { formatCountdown, formatTime, formatDuration } from './timeFormat';

describe('timeFormat utilities', () => {
  describe('formatCountdown', () => {
    it('formats zero seconds correctly', () => {
      expect(formatCountdown(0)).toBe('0s remaining');
      expect(formatCountdown(-5)).toBe('0s remaining');
    });

    it('formats seconds under 60 correctly', () => {
      expect(formatCountdown(1)).toBe('1s remaining');
      expect(formatCountdown(30)).toBe('30s remaining');
      expect(formatCountdown(59)).toBe('59s remaining');
      expect(formatCountdown(59.9)).toBe('1:00 remaining'); // Rounds up to 60s = 1:00
    });

    it('formats minutes and seconds correctly', () => {
      expect(formatCountdown(60)).toBe('1:00 remaining');
      expect(formatCountdown(61)).toBe('1:01 remaining');
      expect(formatCountdown(90)).toBe('1:30 remaining');
      expect(formatCountdown(120)).toBe('2:00 remaining');
      expect(formatCountdown(125)).toBe('2:05 remaining');
    });

    it('formats longer durations correctly', () => {
      expect(formatCountdown(600)).toBe('10:00 remaining');
      expect(formatCountdown(3661)).toBe('61:01 remaining');
      expect(formatCountdown(542)).toBe('9:02 remaining');
    });

    it('handles decimal seconds by rounding up', () => {
      expect(formatCountdown(59.1)).toBe('1:00 remaining'); // 59.1 rounds to 60, which is 1:00
      expect(formatCountdown(60.1)).toBe('1:01 remaining'); // 60.1 rounds to 61, which is 1:01
      expect(formatCountdown(90.9)).toBe('1:31 remaining');
    });
  });

  describe('formatTime', () => {
    it('formats zero time correctly', () => {
      expect(formatTime(0)).toBe('0:00');
    });

    it('formats seconds under 60 correctly', () => {
      expect(formatTime(1)).toBe('0:01');
      expect(formatTime(30)).toBe('0:30');
      expect(formatTime(59)).toBe('0:59');
    });

    it('formats minutes and seconds correctly', () => {
      expect(formatTime(60)).toBe('1:00');
      expect(formatTime(61)).toBe('1:01');
      expect(formatTime(90)).toBe('1:30');
      expect(formatTime(125)).toBe('2:05');
    });

    it('handles decimal seconds by flooring', () => {
      expect(formatTime(59.9)).toBe('0:59');
      expect(formatTime(60.9)).toBe('1:00');
      expect(formatTime(90.1)).toBe('1:30');
    });

    it('pads seconds with leading zero', () => {
      expect(formatTime(65)).toBe('1:05');
      expect(formatTime(601)).toBe('10:01');
      expect(formatTime(3605)).toBe('60:05');
    });
  });

  describe('formatDuration', () => {
    it('formats zero duration correctly', () => {
      expect(formatDuration(0)).toBe('0:00');
      expect(formatDuration(-5)).toBe('0:00');
    });

    it('formats positive durations correctly', () => {
      expect(formatDuration(30)).toBe('0:30');
      expect(formatDuration(90)).toBe('1:30');
      expect(formatDuration(600)).toBe('10:00');
    });

    it('uses formatTime internally', () => {
      // Test that formatDuration behaves the same as formatTime for positive values
      expect(formatDuration(125)).toBe(formatTime(125));
      expect(formatDuration(3661)).toBe(formatTime(3661));
    });
  });

  describe('Real-world scenarios', () => {
    it('handles typical audio durations', () => {
      // 10-minute audio file
      expect(formatCountdown(600)).toBe('10:00 remaining');
      expect(formatTime(600)).toBe('10:00');

      // 5 minutes 42 seconds remaining
      expect(formatCountdown(342)).toBe('5:42 remaining');

      // Last minute countdown
      expect(formatCountdown(59)).toBe('59s remaining');
      expect(formatCountdown(30)).toBe('30s remaining');
      expect(formatCountdown(10)).toBe('10s remaining');
      expect(formatCountdown(1)).toBe('1s remaining');
    });

    it('handles edge cases around minute boundaries', () => {
      expect(formatCountdown(59.1)).toBe('1:00 remaining'); // 59.1 rounds to 60s = 1:00
      expect(formatCountdown(60.1)).toBe('1:01 remaining'); // 60.1 rounds to 61s = 1:01
      expect(formatCountdown(119.9)).toBe('2:00 remaining'); // 119.9 rounds to 120s = 2:00
      expect(formatCountdown(120.1)).toBe('2:01 remaining'); // 120.1 rounds to 121s = 2:01
    });

    it('formats very long durations', () => {
      // 1 hour
      expect(formatCountdown(3600)).toBe('60:00 remaining');
      expect(formatTime(3600)).toBe('60:00');

      // 1 hour 30 minutes
      expect(formatCountdown(5400)).toBe('90:00 remaining');
      expect(formatTime(5400)).toBe('90:00');
    });
  });
});
